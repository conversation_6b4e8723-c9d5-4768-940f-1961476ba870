package migration

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"

	"golang.org/x/text/encoding/japanese"
	"golang.org/x/text/transform"
	"gorm.io/gorm"

	basic_price "github.com/Sera-Global/be-nbs-accounting-system/domain/basic_price"
)

// BasicPriceParser handles parsing of 料金設定 file
type BasicPriceParser struct {
	BaseParser
}

// NewBasicPriceParser creates a new basic price parser
func NewBasicPriceParser() *BasicPriceParser {
	return &BasicPriceParser{
		BaseParser: BaseParser{tableName: "basic_price"},
	}
}

// Parse parses the basic price configuration file
func (p *BasicPriceParser) Parse(filePath string) ([]map[string]string, error) {
	// This file has a different format, so we need custom parsing
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	// Create decoder for Japanese text
	decoder := japanese.ShiftJIS.NewDecoder()
	reader := transform.NewReader(file, decoder)
	scanner := bufio.NewScanner(reader)

	var lines []string
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line != "" {
			lines = append(lines, line)
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading file: %w", err)
	}

	// Parse the pricing configuration
	pricingData := p.parsePricingConfiguration(lines)

	// Create a single record representing the entire pricing configuration
	record := map[string]string{
		"pricing_data": pricingData,
		"file_name":    "料金設定",
	}

	return []map[string]string{record}, nil
}

// parsePricingConfiguration parses the pricing configuration from lines
func (p *BasicPriceParser) parsePricingConfiguration(lines []string) string {
	pricingConfig := make(map[string]interface{})

	// Parse basic price
	basicPriceRegex := regexp.MustCompile(`基本料金\s+(\d+)\s*円`)
	for _, line := range lines {
		if matches := basicPriceRegex.FindStringSubmatch(line); len(matches) > 1 {
			if price, err := strconv.Atoi(matches[1]); err == nil {
				pricingConfig["basic_price"] = price
			}
		}
	}

	// Parse time-based pricing tables
	pricingConfig["time_pricing"] = p.parseTimePricing(lines)

	// Convert to JSON
	jsonData, _ := json.Marshal(pricingConfig)
	return string(jsonData)
}

// parseTimePricing parses time-based pricing from lines
func (p *BasicPriceParser) parseTimePricing(lines []string) map[string]interface{} {
	timePricing := make(map[string]interface{})

	// Parse different pricing sections
	currentSection := ""
	currentTable := make(map[string]interface{})

	for _, line := range lines {
		// Check for section headers
		if strings.Contains(line, "１次料金") {
			if len(currentTable) > 0 {
				timePricing[currentSection] = currentTable
			}
			currentSection = "primary_pricing"
			currentTable = make(map[string]interface{})
		} else if strings.Contains(line, "２次料金") {
			if len(currentTable) > 0 {
				timePricing[currentSection] = currentTable
			}
			currentSection = "secondary_pricing"
			currentTable = make(map[string]interface{})
		} else if strings.Contains(line, "３次料金") {
			if len(currentTable) > 0 {
				timePricing[currentSection] = currentTable
			}
			currentSection = "tertiary_pricing"
			currentTable = make(map[string]interface{})
		}

		// Parse time ranges and prices
		timeRangeRegex := regexp.MustCompile(`(\d+:\d+)～(\d+:\d+)\s+(\d+)\s+(\d+)\s+(\d+)`)
		if matches := timeRangeRegex.FindStringSubmatch(line); len(matches) > 5 {
			timeRange := fmt.Sprintf("%s-%s", matches[1], matches[2])
			prices := map[string]interface{}{
				"A_block": p.parsePrice(matches[3]),
				"B_block": p.parsePrice(matches[4]),
				"C_block": p.parsePrice(matches[5]),
			}
			currentTable[timeRange] = prices
		}
	}

	// Add the last section
	if len(currentTable) > 0 {
		timePricing[currentSection] = currentTable
	}

	return timePricing
}

// parsePrice parses a price string to integer
func (p *BasicPriceParser) parsePrice(priceStr string) int {
	if price, err := strconv.Atoi(priceStr); err == nil {
		return price
	}
	return 0
}

// Validate validates a basic price record
func (p *BasicPriceParser) Validate(record map[string]string) error {
	if record["pricing_data"] == "" {
		return fmt.Errorf("pricing data is required")
	}
	return nil
}

// BasicPriceMigrator handles migration of basic price data
type BasicPriceMigrator struct {
	basicPriceDomain basic_price.BasicPriceDomainItf
}

// NewBasicPriceMigrator creates a new basic price migrator
func NewBasicPriceMigrator() *BasicPriceMigrator {
	// Initialize basic price domain
	basicPriceResource := basic_price.BasicPriceResource{}
	basicPriceDomain := basic_price.InitBasicPriceDomain(basicPriceResource)

	return &BasicPriceMigrator{
		basicPriceDomain: &basicPriceDomain,
	}
}

// GetDependencies returns the list of tables this migrator depends on
func (m *BasicPriceMigrator) GetDependencies() []string {
	return []string{} // No dependencies
}

// Migrate migrates basic price records to the database
func (m *BasicPriceMigrator) Migrate(ctx context.Context, tx *gorm.DB, records []map[string]string, dryRun bool) (*MigrationResult, error) {
	result := &MigrationResult{
		RecordsInserted: 0,
		Errors:          []string{},
	}

	for i, record := range records {
		// Extract pricing data
		pricingData := record["pricing_data"]
		if pricingData == "" {
			result.Errors = append(result.Errors, fmt.Sprintf("Record %d: missing pricing data", i+1))
			continue
		}

		// Validate JSON
		var jsonTest interface{}
		if err := json.Unmarshal([]byte(pricingData), &jsonTest); err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("Record %d: invalid JSON in pricing data: %v", i+1, err))
			continue
		}

		// Set timestamps
		now := time.Now()

		// Create basic price record
		basicPriceRecord := basic_price.BasicPrice{
			Code:        "DEFAULT",
			Title:       "Default Pricing Configuration",
			Explanation: "Legacy pricing configuration migrated from 料金設定 file",
			PriceJson:   pricingData,
			CreatedAt:   now,
			UpdatedAt:   now,
			DeletedAt:   nil,
		}

		// Insert record (skip in dry run mode)
		if !dryRun {
			err := tx.WithContext(ctx).Create(&basicPriceRecord).Error
			if err != nil {
				result.Errors = append(result.Errors, fmt.Sprintf("Record %d: database error: %v", i+1, err))
				continue
			}
		}

		result.RecordsInserted++
	}

	return result, nil
}

// CheckExistingBasicPrice checks if basic price records already exist in the database
func (m *BasicPriceMigrator) CheckExistingBasicPrice(ctx context.Context, tx *gorm.DB) (map[string]bool, error) {
	var existingRecords []basic_price.BasicPrice
	err := tx.WithContext(ctx).Select("code").Find(&existingRecords).Error
	if err != nil {
		return nil, fmt.Errorf("failed to check existing basic price records: %w", err)
	}

	existing := make(map[string]bool)
	for _, record := range existingRecords {
		existing[record.Code] = true
	}

	return existing, nil
}

// PreMigrationValidation performs validation before migration
func (m *BasicPriceMigrator) PreMigrationValidation(ctx context.Context, tx *gorm.DB, records []map[string]string) error {
	// Check if table exists
	if !tx.Migrator().HasTable(&basic_price.BasicPrice{}) {
		return fmt.Errorf("basic_price table does not exist - please run database migrations first")
	}

	// Check if DEFAULT code already exists
	var count int64
	err := tx.WithContext(ctx).Model(&basic_price.BasicPrice{}).Where("code = ? AND deleted_at IS NULL", "DEFAULT").Count(&count).Error
	if err != nil {
		return fmt.Errorf("failed to check existing DEFAULT basic price: %w", err)
	}

	if count > 0 {
		return fmt.Errorf("DEFAULT basic price configuration already exists")
	}

	return nil
}

// PostMigrationValidation performs validation after migration
func (m *BasicPriceMigrator) PostMigrationValidation(ctx context.Context, tx *gorm.DB, expectedCount int) error {
	var count int64
	err := tx.WithContext(ctx).Model(&basic_price.BasicPrice{}).Where("deleted_at IS NULL").Count(&count).Error
	if err != nil {
		return fmt.Errorf("failed to count basic price records: %w", err)
	}

	if int(count) < expectedCount {
		return fmt.Errorf("expected at least %d basic price records, but found %d", expectedCount, count)
	}

	return nil
}
