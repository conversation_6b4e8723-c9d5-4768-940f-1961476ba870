package migration

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"

	"golang.org/x/text/encoding/japanese"
	"golang.org/x/text/transform"
	"gorm.io/gorm"

	basic_price "github.com/Sera-Global/be-nbs-accounting-system/domain/basic_price"
)

// BasicPriceParser handles parsing of 料金設定 file
type BasicPriceParser struct {
	BaseParser
}

// NewBasicPriceParser creates a new basic price parser
func NewBasicPriceParser() *BasicPriceParser {
	return &BasicPriceParser{
		BaseParser: BaseParser{tableName: "basic_price"},
	}
}

// Parse parses the basic price configuration file
func (p *BasicPriceParser) Parse(filePath string) ([]map[string]string, error) {
	// This file has a different format, so we need custom parsing
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	// Create decoder for Japanese text
	decoder := japanese.ShiftJIS.NewDecoder()
	reader := transform.NewReader(file, decoder)
	scanner := bufio.NewScanner(reader)

	var lines []string
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line != "" {
			lines = append(lines, line)
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading file: %w", err)
	}

	// Parse the pricing configuration and create separate records for each block
	return p.parsePricingBlocks(lines)
}

// parsePricingBlocks parses the pricing configuration and creates separate records for each block
func (p *BasicPriceParser) parsePricingBlocks(lines []string) ([]map[string]string, error) {
	var records []map[string]string

	// Parse the pricing sections and extract time-based pricing for each block
	pricingSections := p.extractPricingSections(lines)

	// Create records for each pricing block (A, B, C) in each section
	for sectionName, sectionData := range pricingSections {
		for blockName, timeRanges := range sectionData {
			// Convert time ranges to hourly pricing array
			hourlyPricing := p.convertToHourlyPricing(timeRanges)

			// Create JSON for this block
			pricingJSON, err := json.Marshal(hourlyPricing)
			if err != nil {
				return nil, fmt.Errorf("failed to marshal pricing data for %s-%s: %w", sectionName, blockName, err)
			}

			// Create record for this block
			record := map[string]string{
				"block_code":   fmt.Sprintf("%s_%s", sectionName, blockName),
				"section_name": sectionName,
				"block_name":   blockName,
				"pricing_json": string(pricingJSON),
			}
			records = append(records, record)
		}
	}

	return records, nil
}

// extractPricingSections extracts pricing sections from the file lines
func (p *BasicPriceParser) extractPricingSections(lines []string) map[string]map[string]map[string]int {
	sections := make(map[string]map[string]map[string]int)
	currentSection := ""

	for i, line := range lines {
		// Detect section headers
		if strings.Contains(line, "１次料金") || strings.Contains(line, "1次料金") {
			currentSection = "primary"
		} else if strings.Contains(line, "２次料金") || strings.Contains(line, "2次料金") {
			currentSection = "secondary"
		} else if strings.Contains(line, "３次料金") || strings.Contains(line, "3次料金") {
			currentSection = "tertiary"
		}

		if currentSection == "" {
			continue
		}

		// Parse time range and prices
		// Look for patterns like: "6:00～8:00" followed by prices
		timeRangeRegex := regexp.MustCompile(`(\d{1,2}:\d{2})～(\d{1,2}:\d{2})`)
		if matches := timeRangeRegex.FindStringSubmatch(line); len(matches) > 2 {
			startTime := matches[1]
			endTime := matches[2]
			timeRange := fmt.Sprintf("%s-%s", startTime, endTime)

			// Look for prices in the next line or same line
			pricesLine := line
			if i+1 < len(lines) {
				nextLine := strings.TrimSpace(lines[i+1])
				if nextLine != "" && !strings.Contains(nextLine, ":") {
					pricesLine = nextLine
				}
			}

			// Extract prices (A, B, C blocks)
			priceRegex := regexp.MustCompile(`(\d+)\s+(\d+)\s+(\d+)`)
			if priceMatches := priceRegex.FindStringSubmatch(pricesLine); len(priceMatches) > 3 {
				if sections[currentSection] == nil {
					sections[currentSection] = make(map[string]map[string]int)
				}

				// Store prices for each block
				blocks := []string{"A", "B", "C"}
				for j, block := range blocks {
					if sections[currentSection][block] == nil {
						sections[currentSection][block] = make(map[string]int)
					}
					if price, err := strconv.Atoi(priceMatches[j+1]); err == nil {
						sections[currentSection][block][timeRange] = price
					}
				}
			}
		}
	}

	return sections
}

// convertToHourlyPricing converts time ranges to hourly pricing array
func (p *BasicPriceParser) convertToHourlyPricing(timeRanges map[string]int) []map[string]interface{} {
	var hourlyPricing []map[string]interface{}

	// Create 24-hour pricing array
	for hour := 0; hour < 24; hour++ {
		hourStr := fmt.Sprintf("%02d:00", hour)
		price := p.getPriceForHour(hour, timeRanges)

		hourlyPricing = append(hourlyPricing, map[string]interface{}{
			"hour":  hourStr,
			"price": price,
		})
	}

	return hourlyPricing
}

// getPriceForHour determines the price for a specific hour based on time ranges
func (p *BasicPriceParser) getPriceForHour(hour int, timeRanges map[string]int) int {
	for timeRange, price := range timeRanges {
		if p.isHourInRange(hour, timeRange) {
			return price
		}
	}
	return 0 // Default price if no range matches
}

// isHourInRange checks if an hour falls within a time range
func (p *BasicPriceParser) isHourInRange(hour int, timeRange string) bool {
	parts := strings.Split(timeRange, "-")
	if len(parts) != 2 {
		return false
	}

	startHour := p.parseHour(parts[0])
	endHour := p.parseHour(parts[1])

	// Handle overnight ranges (e.g., 22:00-6:00)
	if startHour > endHour {
		return hour >= startHour || hour < endHour
	}

	return hour >= startHour && hour < endHour
}

// parseHour extracts hour from time string (e.g., "6:00" -> 6)
func (p *BasicPriceParser) parseHour(timeStr string) int {
	parts := strings.Split(timeStr, ":")
	if len(parts) > 0 {
		if hour, err := strconv.Atoi(parts[0]); err == nil {
			return hour
		}
	}
	return 0
}

// Validate validates a basic price record
func (p *BasicPriceParser) Validate(record map[string]string) error {
	if record["pricing_json"] == "" {
		return fmt.Errorf("pricing JSON is required")
	}
	if record["block_code"] == "" {
		return fmt.Errorf("block code is required")
	}
	return nil
}

// BasicPriceMigrator handles migration of basic price data
type BasicPriceMigrator struct {
	basicPriceDomain basic_price.BasicPriceDomainItf
}

// NewBasicPriceMigrator creates a new basic price migrator
func NewBasicPriceMigrator() *BasicPriceMigrator {
	// Initialize basic price domain
	basicPriceResource := basic_price.BasicPriceResource{}
	basicPriceDomain := basic_price.InitBasicPriceDomain(basicPriceResource)

	return &BasicPriceMigrator{
		basicPriceDomain: &basicPriceDomain,
	}
}

// GetDependencies returns the list of tables this migrator depends on
func (m *BasicPriceMigrator) GetDependencies() []string {
	return []string{} // No dependencies
}

// Migrate migrates basic price records to the database
func (m *BasicPriceMigrator) Migrate(ctx context.Context, tx *gorm.DB, records []map[string]string, dryRun bool) (*MigrationResult, error) {
	result := &MigrationResult{
		RecordsInserted: 0,
		Errors:          []string{},
	}

	// Track used codes to ensure uniqueness
	usedCodes := make(map[string]int)

	for i, record := range records {
		// Extract data
		blockCode := record["block_code"]
		sectionName := record["section_name"]
		blockName := record["block_name"]
		pricingJSON := record["pricing_json"]

		if blockCode == "" {
			result.Errors = append(result.Errors, fmt.Sprintf("Record %d: missing block code", i+1))
			continue
		}

		if pricingJSON == "" {
			result.Errors = append(result.Errors, fmt.Sprintf("Record %d: missing pricing JSON", i+1))
			continue
		}

		// Validate JSON
		var jsonTest interface{}
		if err := json.Unmarshal([]byte(pricingJSON), &jsonTest); err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("Record %d: invalid JSON in pricing data: %v", i+1, err))
			continue
		}

		// Ensure code uniqueness
		baseCode := blockCode
		if count, exists := usedCodes[baseCode]; exists {
			blockCode = fmt.Sprintf("%s_%d", baseCode, count+1)
		}
		usedCodes[baseCode]++

		// Set timestamps
		now := time.Now()

		// Create title and explanation
		title := fmt.Sprintf("%s Block %s Pricing", sectionName, blockName)
		explanation := fmt.Sprintf("Legacy pricing configuration for %s block %s migrated from 料金設定 file", sectionName, blockName)

		// Create basic price record
		basicPriceRecord := basic_price.BasicPrice{
			Code:        blockCode,
			Title:       title,
			Explanation: explanation,
			PriceJson:   pricingJSON,
			CreatedAt:   now,
			UpdatedAt:   now,
			DeletedAt:   nil,
		}

		// Insert record (skip in dry run mode)
		if !dryRun {
			err := tx.WithContext(ctx).Create(&basicPriceRecord).Error
			if err != nil {
				result.Errors = append(result.Errors, fmt.Sprintf("Record %d: database error: %v", i+1, err))
				continue
			}
		}

		result.RecordsInserted++
	}

	return result, nil
}

// CheckExistingBasicPrice checks if basic price records already exist in the database
func (m *BasicPriceMigrator) CheckExistingBasicPrice(ctx context.Context, tx *gorm.DB) (map[string]bool, error) {
	var existingRecords []basic_price.BasicPrice
	err := tx.WithContext(ctx).Select("code").Find(&existingRecords).Error
	if err != nil {
		return nil, fmt.Errorf("failed to check existing basic price records: %w", err)
	}

	existing := make(map[string]bool)
	for _, record := range existingRecords {
		existing[record.Code] = true
	}

	return existing, nil
}

// PreMigrationValidation performs validation before migration
func (m *BasicPriceMigrator) PreMigrationValidation(ctx context.Context, tx *gorm.DB, records []map[string]string) error {
	// Check if table exists
	if !tx.Migrator().HasTable(&basic_price.BasicPrice{}) {
		return fmt.Errorf("basic_price table does not exist - please run database migrations first")
	}

	// Check for duplicate block codes in the input data
	codes := make(map[string]int)
	for i, record := range records {
		code := record["block_code"]
		if code == "" {
			continue
		}

		if existingIndex, exists := codes[code]; exists {
			return fmt.Errorf("duplicate block code '%s' found in records %d and %d", code, existingIndex+1, i+1)
		}
		codes[code] = i
	}

	// Check if any of the block codes already exist in the database
	for code := range codes {
		var count int64
		err := tx.WithContext(ctx).Model(&basic_price.BasicPrice{}).Where("code = ? AND deleted_at IS NULL", code).Count(&count).Error
		if err != nil {
			return fmt.Errorf("failed to check existing basic price with code '%s': %w", code, err)
		}

		if count > 0 {
			return fmt.Errorf("basic price configuration with code '%s' already exists", code)
		}
	}

	return nil
}

// PostMigrationValidation performs validation after migration
func (m *BasicPriceMigrator) PostMigrationValidation(ctx context.Context, tx *gorm.DB, expectedCount int) error {
	var count int64
	err := tx.WithContext(ctx).Model(&basic_price.BasicPrice{}).Where("deleted_at IS NULL").Count(&count).Error
	if err != nil {
		return fmt.Errorf("failed to count basic price records: %w", err)
	}

	if int(count) < expectedCount {
		return fmt.Errorf("expected at least %d basic price records, but found %d", expectedCount, count)
	}

	return nil
}
