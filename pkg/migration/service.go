package migration

import (
	"context"
	"fmt"
	"log"
	"path/filepath"
	"time"

	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"gorm.io/gorm"
)

type Config struct {
	DataDir string
	DryRun  bool
	Verbose bool
}

type MigrationService struct {
	config    *Config
	db        *gorm.DB
	logger    *log.Logger
	parsers   map[string]FileParser
	migrators map[string]DataMigrator
}

type MigrationResult struct {
	FileName        string
	RecordsRead     int
	RecordsValid    int
	RecordsSkipped  int
	RecordsInserted int
	Errors          []string
	Duration        time.Duration
}

// FileParser interface for parsing different legacy file formats
type FileParser interface {
	Parse(filePath string) ([]map[string]string, error)
	GetTableName() string
	Validate(record map[string]string) error
}

// DataMigrator interface for migrating parsed data to database
type DataMigrator interface {
	Migrate(ctx context.Context, tx *gorm.DB, records []map[string]string, dryRun bool) (*MigrationResult, error)
	GetDependencies() []string
}

// NewMigrationService creates a new migration service instance
func NewMigrationService(config *Config) *MigrationService {
	service := &MigrationService{
		config:    config,
		db:        dbmanager.Manager(),
		parsers:   make(map[string]FileParser),
		migrators: make(map[string]DataMigrator),
	}

	// Register parsers and migrators
	service.registerParsersAndMigrators()

	return service
}

// MigrateAll migrates all files in the recommended order
func (s *MigrationService) MigrateAll(ctx context.Context) error {
	// Define migration order based on dependencies
	migrationOrder := []string{
		"消費税率マスタ",
		"所得税税額マスタ",
		"資格マスタ",
		"料金設定",
		"地区マスタ", // This will create both block and district
		"ユーザーマスタ",
		"作業員マスタ",
		"顧客マスタ_Ordering Company Master",
		"遠方料金マスタ",
		"遠方出張諸手当マスタ",
	}

	fmt.Printf("Starting migration of %d files...\n", len(migrationOrder))

	var allResults []*MigrationResult
	startTime := time.Now()

	for i, fileName := range migrationOrder {
		fmt.Printf("\n[%d/%d] Migrating: %s\n", i+1, len(migrationOrder), fileName)

		result, err := s.migrateFileInternal(ctx, fileName)
		if err != nil {
			return fmt.Errorf("failed to migrate %s: %w", fileName, err)
		}

		allResults = append(allResults, result)
		s.printResult(result)
	}

	// Print summary
	s.printSummary(allResults, time.Since(startTime))
	return nil
}

// MigrateFile migrates a specific file
func (s *MigrationService) MigrateFile(ctx context.Context, fileName string) error {
	fmt.Printf("Starting migration of file: %s\n", fileName)

	result, err := s.migrateFileInternal(ctx, fileName)
	if err != nil {
		return err
	}

	s.printResult(result)
	return nil
}

// migrateFileInternal handles the actual migration logic
func (s *MigrationService) migrateFileInternal(ctx context.Context, fileName string) (*MigrationResult, error) {
	startTime := time.Now()

	// Check if parser exists for this file
	parser, exists := s.parsers[fileName]
	if !exists {
		return nil, fmt.Errorf("no parser found for file: %s", fileName)
	}

	// Check if migrator exists for this file
	migrator, exists := s.migrators[fileName]
	if !exists {
		return nil, fmt.Errorf("no migrator found for file: %s", fileName)
	}

	// Build file path
	filePath := filepath.Join(s.config.DataDir, fileName)

	// Parse the file
	if s.config.Verbose {
		fmt.Printf("  Parsing file: %s\n", filePath)
	}

	records, err := parser.Parse(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to parse file: %w", err)
	}

	if s.config.Verbose {
		fmt.Printf("  Parsed %d records\n", len(records))
	}

	// Validate records
	validRecords := make([]map[string]string, 0, len(records))
	var validationErrors []string

	for i, record := range records {
		if err := parser.Validate(record); err != nil {
			validationErrors = append(validationErrors, fmt.Sprintf("Record %d: %v", i+1, err))
			continue
		}
		validRecords = append(validRecords, record)
	}

	if s.config.Verbose && len(validationErrors) > 0 {
		fmt.Printf("  Validation errors: %d\n", len(validationErrors))
	}

	// Start database transaction
	tx := s.db.Begin()
	if tx.Error != nil {
		return nil, fmt.Errorf("failed to start transaction: %w", tx.Error)
	}

	// Migrate the data
	result, err := migrator.Migrate(ctx, tx, validRecords, s.config.DryRun)
	if err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("migration failed: %w", err)
	}

	// Commit or rollback based on dry run mode
	if s.config.DryRun {
		tx.Rollback()
		if s.config.Verbose {
			fmt.Printf("  Dry run completed - changes rolled back\n")
		}
	} else {
		if err := tx.Commit().Error; err != nil {
			return nil, fmt.Errorf("failed to commit transaction: %w", err)
		}
	}

	// Set additional result fields
	result.FileName = fileName
	result.RecordsRead = len(records)
	result.RecordsValid = len(validRecords)
	result.RecordsSkipped = len(records) - len(validRecords)
	result.Errors = validationErrors
	result.Duration = time.Since(startTime)

	return result, nil
}

// registerParsersAndMigrators registers all available parsers and migrators
func (s *MigrationService) registerParsersAndMigrators() {
	// Register qualification parser and migrator (WORKING)
	qualificationParser := NewQualificationParser()
	qualificationMigrator := NewQualificationMigrator()
	s.parsers["資格マスタ"] = qualificationParser
	s.migrators["資格マスタ"] = qualificationMigrator

	// Register user master parser and migrator (WORKING)
	userMasterParser := NewUserMasterParser()
	userMasterMigrator := NewUserMasterMigrator()
	s.parsers["ユーザーマスタ"] = userMasterParser
	s.migrators["ユーザーマスタ"] = userMasterMigrator

	// Register worker master parser and migrator (WORKING)
	workerMasterParser := NewWorkerMasterParser()
	workerMasterMigrator := NewWorkerMasterMigrator()
	s.parsers["作業員マスタ"] = workerMasterParser
	s.migrators["作業員マスタ"] = workerMasterMigrator

	// Register distant fee parser and migrator (WORKING)
	distantFeeParser := NewDistantFeeParser()
	distantFeeMigrator := NewDistantFeeMigrator()
	s.parsers["遠方料金マスタ"] = distantFeeParser
	s.migrators["遠方料金マスタ"] = distantFeeMigrator

	// Register distant travel allowance parser and migrator (WORKING)
	distantTravelAllowanceParser := NewDistantTravelAllowanceParser()
	distantTravelAllowanceMigrator := NewDistantTravelAllowanceMigrator()
	s.parsers["遠方出張諸手当マスタ"] = distantTravelAllowanceParser
	s.migrators["遠方出張諸手当マスタ"] = distantTravelAllowanceMigrator

	// TODO: Fix import issues for these migrations:
	// - 消費税率マスタ (consumption tax)
	// - 所得税税額マスタ (income tax)
	// - 料金設定 (basic price)
	// - 地区マスタ (district/block)
	// - 顧客マスタ_Ordering Company Master (customer)
}

// printResult prints the migration result
func (s *MigrationService) printResult(result *MigrationResult) {
	fmt.Printf("  Results:\n")
	fmt.Printf("    Records read: %d\n", result.RecordsRead)
	fmt.Printf("    Records valid: %d\n", result.RecordsValid)
	fmt.Printf("    Records skipped: %d\n", result.RecordsSkipped)
	fmt.Printf("    Records inserted: %d\n", result.RecordsInserted)
	fmt.Printf("    Duration: %v\n", result.Duration)

	if len(result.Errors) > 0 {
		fmt.Printf("    Errors: %d\n", len(result.Errors))
		if s.config.Verbose {
			for _, err := range result.Errors {
				fmt.Printf("      - %s\n", err)
			}
		}
	}
}

// printSummary prints the overall migration summary
func (s *MigrationService) printSummary(results []*MigrationResult, totalDuration time.Duration) {
	fmt.Printf("\n=== Migration Summary ===\n")

	totalRead := 0
	totalValid := 0
	totalSkipped := 0
	totalInserted := 0
	totalErrors := 0

	for _, result := range results {
		totalRead += result.RecordsRead
		totalValid += result.RecordsValid
		totalSkipped += result.RecordsSkipped
		totalInserted += result.RecordsInserted
		totalErrors += len(result.Errors)
	}

	fmt.Printf("Files processed: %d\n", len(results))
	fmt.Printf("Total records read: %d\n", totalRead)
	fmt.Printf("Total records valid: %d\n", totalValid)
	fmt.Printf("Total records skipped: %d\n", totalSkipped)
	fmt.Printf("Total records inserted: %d\n", totalInserted)
	fmt.Printf("Total errors: %d\n", totalErrors)
	fmt.Printf("Total duration: %v\n", totalDuration)

	if s.config.DryRun {
		fmt.Printf("\n*** DRY RUN MODE - NO CHANGES WERE MADE ***\n")
	}
}
