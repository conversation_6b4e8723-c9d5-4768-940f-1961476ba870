-- Validation queries for data migration
-- Run these queries after migration to verify data integrity

-- 1. Check qualification table
SELECT 
    'qualification' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN deleted_at IS NULL THEN 1 END) as active_records,
    MIN(created_at) as earliest_created,
    MAX(updated_at) as latest_updated
FROM qualification;

-- Sample qualification records
SELECT 
    code, 
    title, 
    add_claim, 
    paid_amount,
    created_at
FROM qualification 
WHERE deleted_at IS NULL 
ORDER BY created_at 
LIMIT 5;

-- Check for duplicate codes
SELECT 
    code, 
    COUNT(*) as count
FROM qualification 
WHERE deleted_at IS NULL
GROUP BY code 
HAVING COUNT(*) > 1;

-- Check for missing required fields
SELECT 
    'Missing codes' as issue,
    COUNT(*) as count
FROM qualification 
WHERE code IS NULL OR code = '';

SELECT 
    'Missing titles' as issue,
    COUNT(*) as count
FROM qualification 
WHERE title IS NULL OR title = '';

-- Check data ranges
SELECT 
    'add_claim_stats' as metric,
    MIN(add_claim) as min_value,
    MAX(add_claim) as max_value,
    AVG(add_claim) as avg_value
FROM qualification 
WHERE deleted_at IS NULL;

SELECT 
    'paid_amount_stats' as metric,
    MIN(paid_amount) as min_value,
    MAX(paid_amount) as max_value,
    AVG(paid_amount) as avg_value
FROM qualification 
WHERE deleted_at IS NULL;

-- Check for potential encoding issues (Japanese characters)
SELECT 
    code,
    title,
    LENGTH(title) as title_length,
    CASE 
        WHEN title ~ '[^\x00-\x7F]' THEN 'Contains non-ASCII'
        ELSE 'ASCII only'
    END as encoding_check
FROM qualification 
WHERE deleted_at IS NULL
ORDER BY title_length DESC
LIMIT 10;

-- Summary report
SELECT 
    'MIGRATION VALIDATION SUMMARY' as report_type,
    (SELECT COUNT(*) FROM qualification WHERE deleted_at IS NULL) as total_qualifications,
    (SELECT COUNT(DISTINCT code) FROM qualification WHERE deleted_at IS NULL) as unique_codes,
    (SELECT COUNT(*) FROM qualification WHERE deleted_at IS NULL AND (code IS NULL OR code = '')) as missing_codes,
    (SELECT COUNT(*) FROM qualification WHERE deleted_at IS NULL AND (title IS NULL OR title = '')) as missing_titles,
    CASE 
        WHEN (SELECT COUNT(*) FROM qualification WHERE deleted_at IS NULL) > 0 THEN 'SUCCESS'
        ELSE 'FAILED - No records found'
    END as migration_status;
