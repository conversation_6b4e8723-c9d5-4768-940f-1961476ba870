# Data Migration Guide

This guide explains how to use the data migration system to migrate legacy data from `nbs_master_data/` files to the new database schema.

## Overview

The migration system provides a CLI-based tool that can:
- Parse legacy data files in the standard format
- Transform data according to the new schema requirements
- Validate data before insertion
- Provide dry-run mode for safe testing
- Handle errors gracefully with detailed logging

## Quick Start

### 1. Prerequisites

- Go 1.24.4 or later
- PostgreSQL database with the new schema migrated
- Legacy data files in `nbs_master_data/` directory
- Environment variables configured (see `.env` file)

### 2. Build the Migration Tool

```bash
go build -o bin/migrate cmd/migrate/main.go
```

### 3. Test with Dry Run

```bash
# Test migration of qualification data
go run main.go  --data-dir="../../nbs_master_data" --file="資格マスタ" --dry-run --verbose
```

### 4. Execute Migration

```bash
# Migrate qualification data
go run main.go  --data-dir="../../nbs_master_data" --file="資格マスタ" --execute --verbose
```

## Command Line Options

| Option | Description | Example |
|--------|-------------|---------|
| `--file` | Migrate specific file | `--file="資格マスタ"` |
| `--all` | Migrate all files in order | `--all` |
| `--dry-run` | Preview changes without executing | `--dry-run` |
| `--execute` | Execute the migration | `--execute` |
| `--verbose` | Enable detailed logging | `--verbose` |
| `--data-dir` | Legacy data directory | `--data-dir="nbs_master_data"` |

## Migration Order

The system migrates files in this recommended order to handle dependencies:

1. **消費税率マスタ** → `consumption_tax`
2. **所得税税額マスタ** → `income_tax`
3. **資格マスタ** → `qualification`
4. **料金設定** → `basic_price`
5. **地区マスタ** → `district` + `block`
6. **ユーザーマスタ** → `user`
7. **作業員マスタ** → `user`
8. **顧客マスタ_Ordering Company Master** → `customer` + `department`
9. **遠方料金マスタ** → `distant_fee`
10. **遠方出張諸手当マスタ** → `distant_travel_allowance`

## Usage Examples

### Migrate Single File

The following examples show how to migrate each legacy data file individually, following the recommended migration order:

#### 1. Consumption Tax Master (消費税率マスタ → consumption_tax table)
```bash
# Dry run first to check for issues
go run main.go  --data-dir="../../nbs_master_data" --file="消費税率マスタ" --dry-run --verbose

# Execute if dry run looks good
go run main.go  --data-dir="../../nbs_master_data" --file="消費税率マスタ" --execute --verbose
```

#### 2. Income Tax Master (所得税税額マスタ → income_tax table)
```bash
# Dry run first to check for issues
go run main.go  --data-dir="../../nbs_master_data" --file="所得税税額マスタ" --dry-run --verbose

# Execute if dry run looks good
go run main.go  --data-dir="../../nbs_master_data" --file="所得税税額マスタ" --execute --verbose
```

#### 3. Qualification Master (資格マスタ → qualification table)
```bash
# Dry run first to check for issues
go run main.go  --data-dir="../../nbs_master_data" --file="資格マスタ" --dry-run --verbose

# Execute if dry run looks good
go run main.go  --data-dir="../../nbs_master_data" --file="資格マスタ" --execute --verbose
```

#### 4. Pricing Configuration (料金設定 → basic_price table)
```bash
# Dry run first to check for issues
go run main.go  --data-dir="../../nbs_master_data" --file="料金設定" --dry-run --verbose

# Execute if dry run looks good
go run main.go  --data-dir="../../nbs_master_data" --file="料金設定" --execute --verbose
```

#### 5. District Master (地区マスタ → district + block tables)
```bash
# Dry run first to check for issues
go run main.go  --data-dir="../../nbs_master_data" --file="地区マスタ" --dry-run --verbose

# Execute if dry run looks good
go run main.go  --data-dir="../../nbs_master_data" --file="地区マスタ" --execute --verbose
```

#### 6. User Master (ユーザーマスタ → user table)
```bash
# Dry run first to check for issues
go run main.go  --data-dir="../../nbs_master_data" --file="ユーザーマスタ" --dry-run --verbose

# Execute if dry run looks good
go run main.go  --data-dir="../../nbs_master_data" --file="ユーザーマスタ" --execute --verbose
```

#### 7. Worker Master (作業員マスタ → user table)
```bash
# Dry run first to check for issues
go run main.go  --data-dir="../../nbs_master_data" --file="作業員マスタ" --dry-run --verbose

# Execute if dry run looks good
go run main.go  --data-dir="../../nbs_master_data" --file="作業員マスタ" --execute --verbose
```

#### 8. Customer Master (顧客マスタ_Ordering Company Master → customer + department + department_pic tables)
```bash
# Dry run first to check for issues
go run main.go  --data-dir="../../nbs_master_data" --file="顧客マスタ_Ordering Company Master" --dry-run --verbose

# Execute if dry run looks good
go run main.go  --data-dir="../../nbs_master_data" --file="顧客マスタ_Ordering Company Master" --execute --verbose
```

#### 9. Distance Fee Master (遠方料金マスタ → distant_fee table)
```bash
# Dry run first to check for issues
go run main.go  --data-dir="../../nbs_master_data" --file="遠方料金マスタ" --dry-run --verbose

# Execute if dry run looks good
go run main.go  --data-dir="../../nbs_master_data" --file="遠方料金マスタ" --execute --verbose
```

#### 10. Distance Travel Allowance Master (遠方出張諸手当マスタ → distant_travel_allowance table)
```bash
# Dry run first to check for issues
go run main.go  --data-dir="../../nbs_master_data" --file="遠方出張諸手当マスタ" --dry-run --verbose

# Execute if dry run looks good
go run main.go  --data-dir="../../nbs_master_data" --file="遠方出張諸手当マスタ" --execute --verbose
```

### Migrate All Files

```bash
# Dry run all files in recommended order
go run main.go  --data-dir="../../nbs_master_data" --all --dry-run

# Execute all migrations in recommended order
go run main.go  --data-dir="../../nbs_master_data" --all --execute --verbose
```

### Using the Test Script

```bash
# Run the automated test script (tests qualification migration)
./scripts/test-migration.sh
```

## Data Transformations

### Character Encoding
- Legacy files use Japanese encoding (Shift-JIS)
- Automatically converted to UTF-8 for database storage

### Date Formats
- Legacy: `YYYY/MM/DD`
- Database: PostgreSQL `timestamptz` and `date` types
- Timezone: Assumes JST (Japan Standard Time)

### Code Generation
- Generates unique codes from names/titles
- Uses lowercase, underscores for spaces
- Ensures uniqueness with numeric suffixes

### Default Values
- `created_at`/`updated_at`: Current timestamp or parsed from legacy data
- `deleted_at`: NULL for all migrated records
- Missing fields: Appropriate defaults based on data type

## Error Handling

### Common Issues and Solutions

1. **File Not Found**
   ```
   Error: no parser found for file: 資格マスタ
   ```
   - Ensure the file exists in `nbs_master_data/` directory
   - Check file name spelling (including Japanese characters)

2. **Database Connection**
   ```
   Error: failed to start transaction
   ```
   - Check database connection settings in `.env`
   - Ensure database is running and accessible

3. **Validation Errors**
   ```
   Record 5: required field 'FLD_SikakuName' is missing
   ```
   - Review the legacy data file for missing required fields
   - Use `--verbose` flag to see detailed error information

4. **Duplicate Codes**
   - System automatically handles duplicates with numeric suffixes
   - Example: `code`, `code_1`, `code_2`

## Extending the Migration System

### Adding New File Parsers

1. Create a new parser in `pkg/migration/`:
   ```go
   type NewFileParser struct {
       BaseParser
   }
   
   func (p *NewFileParser) Parse(filePath string) ([]map[string]string, error) {
       return p.ParseLegacyFile(filePath)
   }
   
   func (p *NewFileParser) Validate(record map[string]string) error {
       // Add validation logic
   }
   ```

2. Create corresponding migrator:
   ```go
   type NewFileMigrator struct {
       // Add domain dependencies
   }
   
   func (m *NewFileMigrator) Migrate(ctx context.Context, tx *gorm.DB, records []map[string]string, dryRun bool) (*MigrationResult, error) {
       // Add migration logic
   }
   ```

3. Register in `service.go`:
   ```go
   s.parsers["新しいファイル"] = NewNewFileParser()
   s.migrators["新しいファイル"] = NewNewFileMigrator()
   ```

## Troubleshooting

### Enable Debug Logging
```bash
export LOG_LEVEL=debug
go run main.go  --data-dir="../../nbs_master_data" --file="資格マスタ" --dry-run --verbose
```

### Check Database State
```sql
-- Check if records were inserted
SELECT COUNT(*) FROM qualification WHERE deleted_at IS NULL;

-- View sample records
SELECT code, title, add_claim, paid_amount FROM qualification LIMIT 5;
```

### Rollback Migration
```sql
-- If you need to rollback (be careful!)
DELETE FROM qualification WHERE created_at > '2024-01-01';
```

## Performance Considerations

- Migrations use database transactions for safety
- Large files are processed in batches
- Use `--verbose` only for debugging (impacts performance)
- Consider running during low-traffic periods

## Support

For issues or questions:
1. Check the error logs in the console output
2. Review the data analysis document: `docs/data-migration-analysis.md`
3. Use `--dry-run --verbose` to diagnose issues
4. Check database connectivity and schema
